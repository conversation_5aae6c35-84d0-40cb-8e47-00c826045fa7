<?php

namespace app\merchant_staff\controller;

use app\admin\model\mycurrency\lease\Orderdevice;
use app\common\controller\MerchantStaff;
use app\common\library\device\cabinet\Analysis;
use app\common\library\device\cabinet\Control;
use app\common\library\Ems;
use app\common\library\Sms;
use app\common\model\mycurrency\Agent;
use app\common\model\mycurrency\DeviceLattice;
use app\common\model\mycurrency\LeaseOrder;
use app\common\model\mycurrency\LeaseOrderDevice;
use app\common\model\mycurrency\LeaseOrderPay;
use app\common\model\mycurrency\MerchantStore;
use fast\Random;
use think\Config;
use think\Exception;
use think\Validate;
use think\Db;

/**
 * 租赁接口
 */
class Lease extends MerchantStaff
{
    protected $noNeedLogin = [];
    protected $noNeedRight = '*';


    public function _initialize()
    {
        parent::_initialize();
    }

    /**
     * 租赁订单列表
     */
    public function leaseList(){
        $params = $this->request->param();
        $rule = [
            //非必传参数 store_id门店id  mobile会员手机号 sn订单编号 starttime开始时间 endtime结束时间
            ['page', 'require', '页码不能为空'],
            ['pageSize', 'require', '每页查询条数不能为空'],
            ['status',"require|in:0,1,2,3", '查询类型不能为空|查询类型错误'],//查询类型：0=全部，1=租赁中，2=待支付，3=已完成
        ];
        $check = $this->validate($params, $rule);
        if ($check !== true) {
            $this->error($check);
        }

        // 获取时间筛选参数（支持POST和GET方式）
        $starttime = $this->request->param('starttime', ''); // 开始时间
        $endtime = $this->request->param('endtime', ''); // 结束时间
        if($this->identity == 1){//代理商或业务员
            // 获取该代理商及其所有下级代理商ID（递归查询，包含下级代理商）
            $agent_ids = $this->getAgentAndSubordinates($this->agent['id']);
            $store_where = ['agent_id' => ['in', $agent_ids]];
            $store_ids = MerchantStore::where($store_where)->column('id');

            // 如果代理商筛选了特定门店，验证权限并只查询该门店
            if(isset($params['store_id']) && $params['store_id']){
                if(in_array($params['store_id'], $store_ids)){
                    $store_ids = [$params['store_id']]; // 只查询指定的门店
                }else{
                    $this->error('无权限查看该门店数据');
                }
            }
        }elseif ($this->identity == 2){//门店
            $store_ids = [$this->store['id']];
        }
        $where = [
            'store_id' => ['in',$store_ids],
            'deletetime' => null,
        ];
        switch ($params['status']) {
            case 1:
                $where['use_status'] = LeaseOrder::USE_STATUS_JINXINGZHONG;
                break;
            case 2:
                $where['use_status'] = ['in',[LeaseOrder::USE_STATUS_MIANYA,LeaseOrder::USE_STATUS_YAJIN,LeaseOrder::USE_STATUS_ZHIFUXIANFU,LeaseOrder::USE_STATUS_ZHIFUHOUFU,]];
                break;
            case 3:
                $where['use_status'] = LeaseOrder::USE_STATUS_YIWANCHENG;
                break;
            default:
                $where['use_status'] = ['<>',LeaseOrder::USE_STATUS_YIQUXIAO];
                break;
        }

        //判断是否传递了订单编号和会员手机号等其他筛选条件
        $where1=[];
        //判断是否传递了sn（可能是订单编号或手机号）
        if(isset($params['sn']) && $params['sn']){
            // 判断sn是手机号还是订单号
            if(preg_match('/^1[3-9]\d{9}$/', $params['sn'])){
                // 是手机号
                $user_ids = Db::name('user')->where(['mobile' => ['like', '%'.$params['sn'].'%']])->column('id');
                if($user_ids){
                    $where1['user_id'] = ['in', $user_ids];
                }else{
                    $where1['user_id']=-1;
                }
            }else{
                // 是订单号
                $where1['sn'] = ['like', '%'.$params['sn'].'%'];
            }
        }
//pd($starttime);
        // 添加时间筛选条件（按照租赁结束时间return_time筛选）
        if(!empty($starttime)){
            if(empty($endtime)){
                $this->error('结束时间不能为空');
            }
        }
        if (!empty($starttime) && !empty($endtime)) {
            $startTimestamp = strtotime($starttime); // 开始时间戳
//            $endTimestamp = strtotime($endtime); // 结束时间戳

            // 如果 starttime 和 endtime 相同，调整 endtime 为该日期的结束时间
            if ($starttime === $endtime) {
                $endTimestamp = strtotime("$endtime 23:59:59"); // 结束时间戳（当天结束时间）
            } else {
                $endTimestamp = strtotime($endtime); // 结束时间戳
            }


            $where1['return_time'] = ['between', [$startTimestamp, $endTimestamp]];
//            pd($where1);
        }


        $list = LeaseOrder::with([
                'user' => function($query){//会员信息
                    $query->field('id,nickname,mobile');
                },
                'store' => function($query){//门店信息
                    $query->field('id,store_name,status,dorrhead_image');
                },
                'deposit',//免押记录
                'pendingpayment' => function($query){//待支付的租赁费用
                    $query->where([
                        'pay_status' => LeaseOrderPay::PAY_STATUS__DAIZHIFU,
                        'deletetime' => null,
                    ]);
                    $query->order('id desc');
                },
                'rentingingrogressdevice' => function($query){//使用中的设备
                    $query->order('id desc');
                },
            ])
            ->where($where)
            ->where($where1)
            ->limit(($params['page'] - 1) * $params['pageSize'], $params['pageSize'])
            ->order('id desc')
            ->field('*')
            ->select();
//    echo db()->getLastSql();die;
        foreach($list as $k => $v){
            $list[$k] = LeaseOrder::getUseStatusText($v);//订单状态
            $list[$k]['operate'] = LeaseOrder::operationJurisdiction($v);//操作权限
            $list[$k]['use_duration'] = timediff(strtotime($v['lease_time']),strtotime($v['return_time']),'str');//使用时长
        }
        $num = LeaseOrder::where($where)->where($where1)->count();
        if ($num > $params['page'] * $params['pageSize']) {
            $nextpage = true;
        } else {
            $nextpage = false;
        }

        // 计算统计数据（跟随筛选条件变化）
        $totalRevenue = $this->getTotalRevenue($starttime, $endtime, $store_ids); // 营业总收入
        $totalActualRevenue = $this->getTotalActualRevenue($starttime, $endtime, $store_ids); // 总实收
        $totalOrderCount = $this->getTotalOrderCount($starttime, $endtime, $store_ids); // 订单总数

        $this->success('获取成功', [
            'list' => $list,
            'nextpage' => $nextpage,
            'statistics' => [
                'totalRevenue' => number_format($totalRevenue ?? 0, 2), // 营业总收入
                'totalActualRevenue' => number_format($totalActualRevenue ?? 0, 2), // 总实收
                'totalOrderCount' => $totalOrderCount ?? 0, // 订单总数
            ]
        ]);
    }

    /**
     * 获取代理商门店列表（用于前端筛选）
     * 包含该代理商及其所有下级代理商的门店
     */
    public function getStoreList(){
        // 只有代理商身份才能调用此接口
        if($this->identity != 1){
            $this->error('非代理商身份，无权限访问');
        }

        // 获取该代理商及其所有下级代理商ID（递归查询，包含下级代理商）
        $agent_ids = $this->getAgentAndSubordinates($this->agent['id']);

        // 查询门店列表
        $store_where = [
            'agent_id' => ['in', $agent_ids],
            'status' => 1, // 只查询正常状态的门店
            'deletetime' => null // 未删除的门店
        ];

        $storeList = MerchantStore::where($store_where)
            ->field('id, store_name') // 只返回门店ID和门店名称
            ->order('id desc')
            ->select();

        $this->success('获取成功', [
            'list' => $storeList ?? []
        ]);
    }

    /**
     * 租赁订单详情
     */
    public function leaseOrderInfo(){
        $params = $this->request->param();
        $rule = [
            ['order_id', 'require', '订单id不能为空'],
        ];
        $check = $this->validate($params, $rule);
        if ($check !== true) {
            $this->error($check);
        }

        $where = [
            'id' => $params['order_id'],
            'deletetime' => null,
        ];

        $data = LeaseOrder::with([
                'user' => function($query){//会员信息
                    $query->field('id,nickname,mobile');
                },
                'store' => function($query){//门店信息
                    $query->field('id,store_name,status,dorrhead_image');
                },
                'deposit',//免押记录
                'pendingpayment' => function($query){//待支付的租赁费用
                    $query->where([
                        'pay_status' => LeaseOrderPay::PAY_STATUS__DAIZHIFU,
                        'deletetime' => null,
                    ]);
                    $query->order('id desc');
                },
                'rentingingrogressdevice' => function($query){//使用中的设备
                    $query->order('id desc');
                    $query->with(['device' => function($query){
                        $query->field('id,title');
                    },'lattice' => function($query){
                        $query->field('id,number');
                    }]);
                }
            ])
            ->where($where)
            ->field('*')
            ->find();
        $data = LeaseOrder::getUseStatusText($data);
        $data['operate'] = LeaseOrder::operationJurisdiction($data);
        $data['use_duration'] = timediff(strtotime($data['lease_time']),strtotime($data['return_time']),'str');//使用时长
        unset($data['user']['id']);
        $data['device'] = [
            'title' => $data['rentingingrogressdevice']['device']['title'],
            'number' => $data['rentingingrogressdevice']['lattice']['number'],
        ];
        unset($data['rentingingrogressdevice']['device']);
        unset($data['rentingingrogressdevice']['lattice']);
        unset($data['store']['id']);
        unset($data['store']['status_text']);

        $this->success('获取成功', $data);
    }

    /**
     * 结束订单
    */
    public function leaseOrderEnd(){
        $params = $this->request->param();
        $rule = [
            ['order_id', 'require', '订单id不能为空'],
            ['use_end_time', 'require', '结束时间不能为空'],
        ];
        $check = $this->validate($params, $rule);
        if ($check !== true) {
            $this->error($check);
        }
        $where = [
            'id' => $params['order_id'],
            'deletetime' => null,
        ];
        $order = LeaseOrder::where($where)->find();
        if (!$order){
            $this->error('订单不存在');
        }
        if($order->use_status != LeaseOrder::USE_STATUS_JINXINGZHONG){
            $this->error('订单非使用中状态');
        }
        $order_device = LeaseOrderDevice::where(['lease_order_id' => $order->id])->order(['id desc'])->find();
        if (!$order_device){
            $this->error('设备使用记录不存在');
        }
        Db::startTrans();
        try {
            if ($order_device->status == LeaseOrderDevice::STATUS_ZULINZHONG){//如果是租赁中，说明还没有开始计时 需要先将状态调整为使用中
                LeaseOrder::orderStartRentingResult($order_device->id,1,$order_device->createtime);
            }
            //修改租赁设备记录状态
            $order_device->use_end_time = strtotime($params['use_end_time']);
            $order_device->status = LeaseOrderDevice::STATUS_YIGUIHUAN;
            $order_device->save();
            //结束订单
            LeaseOrder::orderEnd($order_device->lease_order_id,strtotime($params['use_end_time']));
            //修改格口状态
            DeviceLattice::where(['id' => $order_device->device_lattice_id])->update([
                'use_status' => DeviceLattice::USE_STATUS_YOUGAN,
            ]);
            Db::commit();
        } catch (Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        $this->success('操作成功');
    }

    /**
     * 获取代理商及其所有下级代理商ID（递归查询）
     * @param int $agent_id 代理商ID
     * @return array 代理商ID数组
     */
    protected function getAgentAndSubordinates($agent_id)
    {
        if (!$agent_id) {
            return [];
        }

        // 初始化结果数组，包含自身
        $result = [$agent_id];

        // 查询直接下级代理商
        $subordinates = $this->getSubordinates($agent_id);

        // 如果有下级，则递归查询下级的下级
        if (!empty($subordinates)) {
            foreach ($subordinates as $sub_id) {
                $result = array_merge($result, $this->getAgentAndSubordinates($sub_id));
            }
        }

        // 去重并返回
        return array_unique($result);
    }

    /**
     * 获取直接下级代理商ID
     * @param int $agent_id 代理商ID
     * @return array 下级代理商ID数组
     */
    protected function getSubordinates($agent_id)
    {
        return Db::name('mycurrency_agent')
            ->where('pid', $agent_id)
            ->where('deletetime', null)
            ->where('status', 1)
            ->column('id');
    }

    /**
     * 获取营业总收入（根据传入的时间范围）
     * @param string $starttime 开始时间
     * @param string $endtime 结束时间
     * @param array $filteredStoreIds 筛选后的门店ID集合
     * @return float 营业总收入
     */
    private function getTotalRevenue($starttime = '', $endtime = '', $filteredStoreIds = [])
    {
        $db = Db::name('mycurrency_lease_order');

        // 如果有传入时间参数，则按时间范围查询
        if (!empty($starttime) && !empty($endtime)) {
            $startTimestamp = strtotime($starttime); // 开始时间戳
            $endTimestamp = strtotime($endtime); // 结束时间戳

            $db->where('return_time', '>=', $startTimestamp)
               ->where('return_time', '<=', $endTimestamp);
        }

        // 如果有门店筛选条件，添加门店筛选
        if (!empty($filteredStoreIds)) {
            $db->where('store_id', 'in', $filteredStoreIds);
        }

        // 查询实际需要支付的金额总和
        $totalRevenue = $db->where('return_time', 'neq', 'null') // 确保有结束租赁时间
                           ->where('return_time', '>', 0) // 确保return_time大于0
                           ->sum('paymen_required');

        return $totalRevenue;
    }

    /**
     * 获取订单总数（根据传入的时间范围）
     * @param string $starttime 开始时间
     * @param string $endtime 结束时间
     * @param array $filteredStoreIds 筛选后的门店ID集合
     * @return int 订单总数
     */
    private function getTotalOrderCount($starttime = '', $endtime = '', $filteredStoreIds = [])
    {
        $db = Db::name('mycurrency_lease_order');

        // 如果有传入时间参数，则按时间范围查询
        if (!empty($starttime) && !empty($endtime)) {
            $startTimestamp = strtotime($starttime); // 开始时间戳
            $endTimestamp = strtotime($endtime); // 结束时间戳

            $db->where('return_time', '>=', $startTimestamp)
               ->where('return_time', '<=', $endTimestamp);
        }

        // 如果有门店筛选条件，添加门店筛选
        if (!empty($filteredStoreIds)) {
            $db->where('store_id', 'in', $filteredStoreIds);
        }

        // 查询订单总数（已完成+进行中的订单）
        $totalOrderCount = $db->whereIn('use_status', [4, 5, 6]) // 订单状态为4,5,6
                              ->count();

        return $totalOrderCount;
    }

    /**
     * 获取总实收（根据传入的时间范围）
     * @param string $starttime 开始时间
     * @param string $endtime 结束时间
     * @param array $filteredStoreIds 筛选后的门店ID集合
     * @return float 总实收金额
     */
    private function getTotalActualRevenue($starttime = '', $endtime = '', $filteredStoreIds = [])
    {
        // 押金支付记录表的总额
        $depositTotal = $this->getDepositTotal($starttime, $endtime, $filteredStoreIds);

        // 租赁费用补交表的总额
        $payTotal = $this->getPayTotal($starttime, $endtime, $filteredStoreIds);

        return $depositTotal + $payTotal;
    }

    /**
     * 获取押金支付记录表的总额（根据传入的时间范围）
     * @param string $starttime 开始时间
     * @param string $endtime 结束时间
     * @param array $filteredStoreIds 筛选后的门店ID集合
     * @return float 押金总额
     */
    private function getDepositTotal($starttime = '', $endtime = '', $filteredStoreIds = [])
    {
        $db = Db::name('mycurrency_lease_deposit');

        // 如果有传入时间参数，则按时间范围查询
        if (!empty($starttime) && !empty($endtime)) {
            $startTimestamp = strtotime($starttime); // 开始时间戳
            $endTimestamp = strtotime($endtime); // 结束时间戳

            $db->where('deduction_time', '>=', $startTimestamp)
                                 ->where('deduction_time', '<=', $endTimestamp);
        }

        // 如果有门店筛选条件，添加门店筛选
        if (!empty($filteredStoreIds)) {
            $db->where('store_id', 'in', $filteredStoreIds);
        }

        // 查询押金扣款总额，确保有扣款时间
        $depositTotal = $db->where('deduction_time', 'neq', 'null') // 确保有扣款时间
                           ->where('deduction_time', '>', 0) // 确保deduction_time大于0
                           ->sum('deduction_fee');

        return $depositTotal ?? 0;
    }

    /**
     * 获取租赁费用补交表的总额（根据传入的时间范围）
     * @param string $starttime 开始时间
     * @param string $endtime 结束时间
     * @param array $filteredStoreIds 筛选后的门店ID集合
     * @return float 补交费用总额
     */
    private function getPayTotal($starttime = '', $endtime = '', $filteredStoreIds = [])
    {
        $db = Db::name('mycurrency_lease_order_pay');

        // 如果有传入时间参数，则按时间范围查询
        if (!empty($starttime) && !empty($endtime)) {
            $startTimestamp = strtotime($starttime); // 开始时间戳
            $endTimestamp = strtotime($endtime); // 结束时间戳

            $db->where('payment_time', '>=', $startTimestamp)
               ->where('payment_time', '<=', $endTimestamp);
        }

        // 如果有门店筛选条件，添加门店筛选
        if (!empty($filteredStoreIds)) {
            $db->where('store_id', 'in', $filteredStoreIds);
        }

        // 查询已支付的补交费用总额
        $payTotal = $db->where('pay_status', 3) // 支付状态为3（已支付）
                       ->where('payment_time', 'neq', 'null') // 确保有支付时间
                       ->where('payment_time', '>', 0) // 确保payment_time大于0
                       ->where('store_id','<>','null')
                       ->sum('money');

        return $payTotal ?? 0;
    }
}
